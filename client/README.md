<h1 align="center">Red Planet Staffing Client</h1>

This is the client application for Red Planet Staffing, the leading staffing marketplace on Mars. Built with React and Material UI, it provides a modern interface for workplaces to post shifts and workers to claim them.

## Technologies

- [TypeScript](https://www.typescriptlang.org/)
- [React](https://react.dev/)
- [Material UI](https://mui.com/)
- [TanStack Query](https://tanstack.com/query/latest)
- [Vite](https://vitejs.dev/)

## Getting Started

### Setup

```bash
# Install dependencies
npm install

# Start development server
npm run start:dev
```
