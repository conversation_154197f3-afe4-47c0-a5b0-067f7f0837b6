import axios, { AxiosInstance } from 'axios';

interface Workplace {
  id: number;
  name: string;
  status: number;
}

interface Shift {
  id: number;
  workplaceId: number;
  workerId: number | null;
  cancelledAt: string | null;
}

interface PaginatedResponse<T> {
  data: T[];
  links: { next?: string };
}

async function fetchAllPages<T>(initialUrl: string, api: AxiosInstance): Promise<T[]> {
  let url = initialUrl;
  let allData: T[] = [];
  
  while (url) {
    console.log(`Fetching: ${url}`);
    const response = await api.get<PaginatedResponse<T>>(url);
    allData = [...allData, ...response.data.data];
    
    // Check if there's a next page
    url = response.data.links.next ? response.data.links.next : '';
    
    // If the next URL is absolute, extract the path
    if (url.startsWith('http')) {
      const urlObj = new URL(url);
      url = urlObj.pathname + urlObj.search;
    }
  }
  
  return allData;
}

async function getTopWorkplaces(limit: number = 3): Promise<void> {
  try {
    console.log(`Finding the top ${limit} most active workplaces...`);
    
    // Set up API client
    const api = axios.create({
      baseURL: 'http://localhost:3000',
    });
    
    // Fetch all workplaces (handling pagination)
    console.log('Fetching workplaces...');
    const workplaces = await fetchAllPages<Workplace>('/workplaces', api);
    console.log(`Found ${workplaces.length} workplaces`);
    
    // Fetch all shifts (handling pagination)
    console.log('Fetching shifts...');
    const shifts = await fetchAllPages<Shift>('/shifts', api);
    console.log(`Found ${shifts.length} shifts`);
    
    // Count shifts per workplace
    const workplaceShiftCounts = new Map<number, number>();
    const workplaceMap = new Map<number, Workplace>();
    
    // Create a map of workplace IDs to workplace objects
    workplaces.forEach(workplace => {
      workplaceMap.set(workplace.id, workplace);
      workplaceShiftCounts.set(workplace.id, 0);
    });
    
    // Count shifts for each workplace
    shifts.forEach(shift => {
      if (!shift.cancelledAt) {
        const currentCount = workplaceShiftCounts.get(shift.workplaceId) || 0;
        workplaceShiftCounts.set(shift.workplaceId, currentCount + 1);
      }
    });
    
    // Debug: Log all workplaces with their status and shift counts
    console.log('\nAll workplaces with shift counts:');
    workplaces.forEach(workplace => {
      console.log(`ID: ${workplace.id}, Name: ${workplace.name}, Status: ${workplace.status}, Shifts: ${workplaceShiftCounts.get(workplace.id) || 0}`);
    });
    
    // Convert to array and sort by shift count
    const topWorkplacesArray = Array.from(workplaceShiftCounts.entries())
      .filter(([workplaceId]) => {
        const workplace = workplaceMap.get(workplaceId);
        return workplace && workplace.status === 0; // Only include active workplaces
      })
      .map(([workplaceId, shiftCount]) => ({
        name: workplaceMap.get(workplaceId)?.name || 'Unknown',
        shifts: shiftCount
      }))
      .sort((a, b) => b.shifts - a.shifts)
      .slice(0, limit);
    
    console.log('\nTop workplaces:');
    console.log(topWorkplacesArray);
    
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('API Error:', error.message);
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', error.response.data);
      } else if (error.request) {
        console.error('No response received. Is the server running?');
      }
    } else {
      console.error('Error fetching top workplaces:', error);
    }
  }
}

// Execute the function with default limit of 3 workplaces
getTopWorkplaces()
  .then(() => console.log('\nScript completed successfully.'))
  .catch(error => console.error('Script failed:', error));
