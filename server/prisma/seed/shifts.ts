import { Prisma } from "@prisma/client";

export const shifts: Prisma.ShiftCreateInput[] = [
  {
    startAt: "2024-06-10T07:00:00.000Z",
    endAt: "2024-06-10T15:00:00.000Z",
    workplace: { connect: { id: 4 } },
  },
  {
    startAt: "2024-06-07T07:00:00.000Z",
    endAt: "2024-06-07T15:00:00.000Z",
    worker: { connect: { id: 2 } },
    workplace: { connect: { id: 5 } },
  },
  {
    startAt: "2024-05-19T15:00:00.000Z",
    endAt: "2024-05-19T23:00:00.000Z",
    worker: { connect: { id: 3 } },
    workplace: { connect: { id: 4 } },
  },
  {
    startAt: "2024-06-12T23:00:00.000Z",
    endAt: "2024-06-13T07:00:00.000Z",
    worker: { connect: { id: 4 } },
    workplace: { connect: { id: 2 } },
  },
  {
    startAt: "2024-06-06T23:00:00.000Z",
    endAt: "2024-06-07T07:00:00.000Z",
    worker: { connect: { id: 5 } },
    workplace: { connect: { id: 4 } },
  },
  {
    startAt: "2024-02-28T07:00:00.000Z",
    endAt: "2024-02-28T15:00:00.000Z",
    worker: { connect: { id: 6 } },
    workplace: { connect: { id: 2 } },
  },
  {
    startAt: "2024-04-15T23:00:00.000Z",
    endAt: "2024-04-16T07:00:00.000Z",
    workplace: { connect: { id: 4 } },
  },
  {
    startAt: "2024-03-25T15:00:00.000Z",
    endAt: "2024-03-25T23:00:00.000Z",
    worker: { connect: { id: 8 } },
    workplace: { connect: { id: 1 } },
  },
  {
    startAt: "2024-01-27T15:00:00.000Z",
    endAt: "2024-01-27T23:00:00.000Z",
    worker: { connect: { id: 9 } },
    workplace: { connect: { id: 3 } },
  },
  {
    startAt: "2024-01-15T23:00:00.000Z",
    endAt: "2024-01-16T07:00:00.000Z",
    worker: { connect: { id: 10 } },
    workplace: { connect: { id: 2 } },
  },
  {
    startAt: "2024-06-14T07:00:00.000Z",
    endAt: "2024-06-14T15:00:00.000Z",
    worker: { connect: { id: 1 } },
    workplace: { connect: { id: 5 } },
  },
  {
    startAt: "2024-06-15T07:00:00.000Z",
    endAt: "2024-06-15T15:00:00.000Z",
    worker: { connect: { id: 2 } },
    workplace: { connect: { id: 1 } },
  },
  {
    startAt: "2024-06-16T07:00:00.000Z",
    endAt: "2024-06-16T15:00:00.000Z",
    workplace: { connect: { id: 2 } },
  },
  {
    startAt: "2024-06-17T07:00:00.000Z",
    endAt: "2024-06-17T15:00:00.000Z",
    worker: { connect: { id: 12 } },
    workplace: { connect: { id: 13 } },
  },
  {
    startAt: "2024-06-18T07:00:00.000Z",
    endAt: "2024-06-18T15:00:00.000Z",
    worker: { connect: { id: 12 } },
    workplace: { connect: { id: 11 } },
  },
  {
    startAt: "2024-06-19T07:00:00.000Z",
    endAt: "2024-06-19T15:00:00.000Z",
    workplace: { connect: { id: 5 } },
  },
  {
    startAt: "2024-06-20T07:00:00.000Z",
    endAt: "2024-06-20T15:00:00.000Z",
    worker: { connect: { id: 7 } },
    workplace: { connect: { id: 14 } },
  },
  {
    startAt: "2024-06-21T07:00:00.000Z",
    endAt: "2024-06-21T15:00:00.000Z",
    worker: { connect: { id: 8 } },
    workplace: { connect: { id: 14 } },
  },
  {
    startAt: "2024-06-22T07:00:00.000Z",
    endAt: "2024-06-22T15:00:00.000Z",
    worker: { connect: { id: 9 } },
    workplace: { connect: { id: 14 } },
  },
  {
    startAt: "2024-06-23T07:00:00.000Z",
    endAt: "2024-06-23T15:00:00.000Z",
    workplace: { connect: { id: 15 } },
  },
  {
    startAt: "2024-06-24T07:00:00.000Z",
    endAt: "2024-06-24T15:00:00.000Z",
    worker: { connect: { id: 15 } },
    workplace: { connect: { id: 16 } },
  },
  {
    startAt: "2024-06-25T07:00:00.000Z",
    endAt: "2024-06-25T15:00:00.000Z",
    worker: { connect: { id: 2 } },
    workplace: { connect: { id: 16 } },
  },
  {
    startAt: "2024-06-26T07:00:00.000Z",
    endAt: "2024-06-26T15:00:00.000Z",
    worker: { connect: { id: 3 } },
    workplace: { connect: { id: 18 } },
  },
  {
    startAt: "2024-06-27T07:00:00.000Z",
    endAt: "2024-06-27T15:00:00.000Z",
    worker: { connect: { id: 4 } },
    workplace: { connect: { id: 18 } },
  },
  {
    startAt: "2024-06-28T07:00:00.000Z",
    endAt: "2024-06-28T15:00:00.000Z",
    worker: { connect: { id: 5 } },
    workplace: { connect: { id: 18 } },
  },
  {
    startAt: "2024-06-29T07:00:00.000Z",
    endAt: "2024-06-29T15:00:00.000Z",
    worker: { connect: { id: 6 } },
    workplace: { connect: { id: 18 } },
  },
  {
    startAt: "2024-06-30T07:00:00.000Z",
    endAt: "2024-06-30T15:00:00.000Z",
    worker: { connect: { id: 7 } },
    workplace: { connect: { id: 18 } },
  },
  {
    startAt: "2024-07-01T07:00:00.000Z",
    endAt: "2024-07-01T15:00:00.000Z",
    workplace: { connect: { id: 18 } },
  },
  {
    startAt: "2024-07-02T07:00:00.000Z",
    endAt: "2024-07-02T15:00:00.000Z",
    worker: { connect: { id: 9 } },
    workplace: { connect: { id: 3 } },
  },
  {
    startAt: "2024-07-03T07:00:00.000Z",
    endAt: "2024-07-03T15:00:00.000Z",
    worker: { connect: { id: 10 } },
    workplace: { connect: { id: 4 } },
  },
];
