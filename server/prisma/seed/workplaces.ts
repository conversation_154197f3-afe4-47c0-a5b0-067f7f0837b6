import { Prisma } from "@prisma/client";

export const workplaces: Prisma.WorkplaceCreateInput[] = [
  {
    name: "Moon Studies Software",
    status: 0,
  },
  {
    name: "Radiant Power Inc",
    status: 0,
  },
  {
    name: "Dust Power LLC",
    status: 2,
  },
  {
    name: "Sun Phosphate Software",
    status: 0,
  },
  {
    name: "Phobos Studies",
    status: 0,
  },
  {
    name: "Orbit Research Labs",
    status: 1,
  },
  {
    name: "Galactic Mining Corp",
    status: 0,
  },
  {
    name: "Lunar Base Network",
    status: 2,
  },
  {
    name: "Starlight Energy Solutions",
    status: 0,
  },
  {
    name: "Quantum Analytics",
    status: 0,
  },
  {
    name: "Deep Space Technologies",
    status: 0,
  },
  {
    name: "Neptune Navigation Ltd",
    status: 0,
  },
  {
    name: "Cosmic Construction Co.",
    status: 0,
  },
  {
    name: "Saturn Systems",
    status: 0,
  },
  {
    name: "Jupiter Networks",
    status: 0,
  },
  {
    name: "Venus Ventures",
    status: 0,
  },
  {
    name: "Mercury Materials",
    status: 0,
  },
  {
    name: "Earth Ecology Enterprises",
    status: 1,
  },
  {
    name: "Pluto Properties",
    status: 0,
  },
];
